@extends('layouts.pwa')

@section('title', 'Pedido Confirmado - ' . $establishment->name)
@section('page-title', 'Confirmação do Pedido')

@php
    $showBackButton = true;
@endphp

@section('content')
    <h2 class="text-[#1b0e0e] tracking-light text-[28px] font-bold leading-tight px-4 text-center pb-3 pt-5">Pedido Recebido!</h2>
    <p class="text-[#1b0e0e] text-base font-normal leading-normal pb-3 pt-1 px-4 text-center">
        Seu pedido foi realizado com sucesso e está sendo preparado. Notificaremos você quando estiver pronto.
    </p>

    @if(isset($order))
        <!-- Order Details -->
        <div class="mx-4 my-6 bg-white rounded-xl p-4 shadow-sm">
            <h3 class="text-[#1b0e0e] text-lg font-bold mb-3">Detalhes do Pedido</h3>

            <div class="space-y-2 mb-4">
                <div class="flex justify-between">
                    <span class="text-[#994d51] text-sm">Número do Pedido:</span>
                    <span class="text-[#1b0e0e] font-medium">#{{ $order->id ?? 'PWA-' . time() }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-[#994d51] text-sm">Cliente:</span>
                    <span class="text-[#1b0e0e] font-medium">{{ $order->customer_name ?? 'Cliente' }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-[#994d51] text-sm">Local:</span>
                    <span class="text-[#1b0e0e] font-medium">{{ $location->display_name }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-[#994d51] text-sm">Status:</span>
                    <span class="text-green-600 font-medium">{{ ucfirst($order->status ?? 'Pendente') }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-[#994d51] text-sm">Horário:</span>
                    <span class="text-[#1b0e0e] font-medium">{{ now()->format('H:i') }}</span>
                </div>
            </div>

            @if(isset($order->items) && $order->items->count() > 0)
                <div class="border-t border-[#f3e7e8] pt-3">
                    <h4 class="text-[#1b0e0e] font-bold mb-2">Itens do Pedido:</h4>
                    @foreach($order->items as $item)
                        <div class="flex justify-between items-center py-1">
                            <div class="flex-1">
                                <span class="text-[#1b0e0e] text-sm">
                                    {{ $item->quantity }}x
                                    @if($item->dish)
                                        {{ $item->dish->name }}
                                    @elseif($item->service)
                                        {{ $item->service->name }}
                                    @endif
                                </span>
                            </div>
                            <span class="text-[#1b0e0e] text-sm font-medium">
                                R$ {{ number_format($item->unit_price * $item->quantity, 2, ',', '.') }}
                            </span>
                        </div>
                    @endforeach

                    <div class="border-t border-[#f3e7e8] pt-2 mt-2">
                        <div class="flex justify-between items-center">
                            <span class="text-[#1b0e0e] font-bold">Total:</span>
                            <span class="text-[#1b0e0e] font-bold">R$ {{ number_format($order->total_amount ?? 0, 2, ',', '.') }}</span>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Estimated Time -->
        <div class="mx-4 mb-6 bg-[#f3e7e8] rounded-xl p-4">
            <div class="text-center">
                <div class="text-[#1b0e0e] text-2xl font-bold mb-1">15-20 min</div>
                <div class="text-[#994d51] text-sm">Tempo estimado de preparo</div>
            </div>
        </div>
    @endif

    <!-- Action Buttons -->
    <div class="px-4 space-y-3">
        <button
            onclick="viewOrderDetails()"
            class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-5 w-full bg-[#e82630] text-[#fcf8f8] text-base font-bold leading-normal tracking-[0.015em]"
        >
            <span class="truncate">Ver Detalhes do Pedido</span>
        </button>

        <button
            onclick="trackOrder()"
            class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-5 w-full bg-[#f3e7e8] text-[#1b0e0e] text-base font-bold leading-normal tracking-[0.015em]"
        >
            <span class="truncate">Acompanhar Pedido</span>
        </button>

        <button
            onclick="goBackToMenu()"
            class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-5 w-full bg-transparent border-2 border-[#1b0e0e] text-[#1b0e0e] text-base font-bold leading-normal tracking-[0.015em]"
        >
            <span class="truncate">Voltar ao Menu</span>
        </button>
    </div>

    <!-- Success Animation -->
    <div id="success-animation" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl p-8 text-center max-w-sm mx-4">
            <div class="text-green-500 text-6xl mb-4">✓</div>
            <h3 class="text-[#1b0e0e] text-xl font-bold mb-2">Sucesso!</h3>
            <p class="text-[#994d51] text-sm">Seu pedido foi confirmado</p>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    // Show success animation on page load
    document.addEventListener('DOMContentLoaded', function() {
        showSuccessAnimation();

        // Clear cart since order was successful
        cart = [];
        updateCartDisplay();
    });

    function showSuccessAnimation() {
        const animation = document.getElementById('success-animation');
        animation.classList.remove('hidden');

        setTimeout(() => {
            animation.classList.add('hidden');
        }, 2000);
    }

    function viewOrderDetails() {
        @if(isset($order))
            // In a real implementation, this would navigate to order details page
            alert('Funcionalidade de detalhes do pedido será implementada');
        @else
            alert('Detalhes do pedido não disponíveis');
        @endif
    }

    function trackOrder() {
        @if(isset($order))
            // In a real implementation, this would navigate to order tracking page
            alert('Funcionalidade de rastreamento será implementada');
        @else
            alert('Rastreamento não disponível');
        @endif
    }

    function goBackToMenu() {
        window.location.href = '{{ route("customer.pwa.menu", [$establishment, $location]) }}';
    }

    // Auto-refresh order status (in a real implementation)
    function startOrderStatusPolling() {
        @if(isset($order))
            setInterval(() => {
                // Poll server for order status updates
                // This would be implemented with actual API calls
                console.log('Checking order status...');
            }, 30000); // Check every 30 seconds
        @endif
    }

    // Start polling when page loads
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(startOrderStatusPolling, 5000); // Start after 5 seconds
    });
</script>
@endsection
