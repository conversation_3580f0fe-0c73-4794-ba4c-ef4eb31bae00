@extends('layouts.pwa')

@section('title', '<PERSON><PERSON>ed<PERSON> - ' . $establishment->name)
@section('page-title', 'Seu Pedido')

@section('content')
    <div id="cart-content">
        <!-- Cart items will be populated by JavaScript -->
        <div id="cart-items-container">
            <!-- Items will be inserted here -->
        </div>

        <!-- Empty cart state -->
        <div id="empty-cart" class="flex flex-col items-center justify-center py-16 px-4 hidden">
            <div class="text-[#994d51] mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M222.14,58.87A8,8,0,0,0,216,56H54.68L49.79,29.14A16,16,0,0,0,34.05,16H16a8,8,0,0,0,0,16h18L59.56,172.29a24,24,0,0,0,5.33,11.27,28,28,0,1,0,44.4,8.44h45.42A27.75,27.75,0,0,0,152,204a28,28,0,1,0,28-28H83.17a8,8,0,0,1-7.87-6.57L72.13,152h116a24,24,0,0,0,23.61-19.71l12.16-66.86A8,8,0,0,0,222.14,58.87ZM96,204a12,12,0,1,1-12-12A12,12,0,0,1,96,204Zm96,0a12,12,0,1,1-12-12A12,12,0,0,1,192,204Zm4-74.57A8,8,0,0,1,188.1,136H69.22L57.59,72H206.41Z"></path>
                </svg>
            </div>
            <h3 class="text-[#1b0e0e] text-lg font-bold mb-2">Carrinho vazio</h3>
            <p class="text-[#994d51] text-center mb-4">Adicione alguns itens ao seu carrinho para continuar</p>
            <a href="{{ route('customer.pwa.menu', [$establishment, $location]) }}" class="bg-[#1b0e0e] text-white px-6 py-3 rounded-xl font-medium">
                Ver Menu
            </a>
        </div>

        <!-- Subtotal section -->
        <div id="subtotal-section" class="hidden">
            <h3 class="text-[#1b0e0e] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Subtotal</h3>
            <div class="flex items-center gap-4 bg-[#fcf8f8] px-4 min-h-14 justify-between">
                <p class="text-[#1b0e0e] text-base font-normal leading-normal flex-1 truncate">Total</p>
                <div class="shrink-0">
                    <p class="text-[#1b0e0e] text-base font-normal leading-normal">R$ <span id="cart-total-display">0,00</span></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Action buttons -->
    <div id="cart-actions" class="hidden">
        <div class="flex justify-stretch">
            <div class="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-between">
                <button
                    onclick="proceedToOrder()"
                    class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-12 px-5 bg-[#ea2832] text-[#fcf8f8] text-base font-bold leading-normal tracking-[0.015em]"
                >
                    <span class="truncate">Fazer Pedido</span>
                </button>
                <button
                    onclick="showQRScanner()"
                    class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-12 px-5 bg-[#f3e7e8] text-[#1b0e0e] text-base font-bold leading-normal tracking-[0.015em]"
                >
                    <span class="truncate">Escanear QR</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Order Form Modal -->
    <div id="order-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="fixed bottom-0 left-0 right-0 bg-[#fcf8f8] rounded-t-xl max-h-[80vh] overflow-y-auto">
            <div class="p-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-[#1b0e0e] text-lg font-bold">Finalizar Pedido</h3>
                    <button onclick="closeOrderModal()" class="text-[#994d51]">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 256 256">
                            <path d="M205.66,194.34a8,8,0,0,1-11.32,11.32L128,139.31,61.66,205.66a8,8,0,0,1-11.32-11.32L116.69,128,50.34,61.66A8,8,0,0,1,61.66,50.34L128,116.69l66.34-66.35a8,8,0,0,1,11.32,11.32L139.31,128Z"></path>
                        </svg>
                    </button>
                </div>

                <form id="order-form">
                    <div class="mb-4">
                        <label for="customer_name" class="block text-[#1b0e0e] text-sm font-medium mb-2">
                            Nome {{ $establishment->type === 'hotel' ? 'do Hóspede' : 'do Cliente' }}
                        </label>
                        <input
                            type="text"
                            id="customer_name"
                            name="customer_name"
                            required
                            class="w-full px-4 py-3 rounded-xl border-none bg-[#f3e7e8] text-[#1b0e0e] placeholder-[#994d51] focus:outline-none focus:ring-2 focus:ring-[#1b0e0e]"
                            placeholder="Digite seu nome"
                        >
                    </div>

                    <div class="mb-4">
                        <label for="special_requests" class="block text-[#1b0e0e] text-sm font-medium mb-2">
                            Observações (opcional)
                        </label>
                        <textarea
                            id="special_requests"
                            name="special_requests"
                            rows="3"
                            class="w-full px-4 py-3 rounded-xl border-none bg-[#f3e7e8] text-[#1b0e0e] placeholder-[#994d51] focus:outline-none focus:ring-2 focus:ring-[#1b0e0e]"
                            placeholder="Alguma observação especial?"
                        ></textarea>
                    </div>

                    <!-- Order Summary -->
                    <div class="mb-4 p-4 bg-[#f3e7e8] rounded-xl">
                        <h4 class="text-[#1b0e0e] font-bold mb-2">Resumo do Pedido</h4>
                        <div id="order-summary-items">
                            <!-- Items will be populated here -->
                        </div>
                        <div class="border-t border-[#994d51] pt-2 mt-2">
                            <div class="flex justify-between items-center">
                                <span class="text-[#1b0e0e] font-bold">Total:</span>
                                <span class="text-[#1b0e0e] font-bold">R$ <span id="order-total">0,00</span></span>
                            </div>
                        </div>
                    </div>

                    <button
                        type="submit"
                        class="w-full bg-[#ea2832] text-white py-3 rounded-xl font-bold"
                    >
                        Confirmar Pedido
                    </button>
                </form>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    // Override the showBackButton variable for this page
    const showBackButton = true;

    function renderCartItems() {
        const container = document.getElementById('cart-items-container');
        const emptyCart = document.getElementById('empty-cart');
        const subtotalSection = document.getElementById('subtotal-section');
        const cartActions = document.getElementById('cart-actions');
        const totalDisplay = document.getElementById('cart-total-display');

        if (cart.length === 0) {
            container.innerHTML = '';
            emptyCart.classList.remove('hidden');
            subtotalSection.classList.add('hidden');
            cartActions.classList.add('hidden');
            return;
        }

        emptyCart.classList.add('hidden');
        subtotalSection.classList.remove('hidden');
        cartActions.classList.remove('hidden');

        let html = '';
        let total = 0;

        cart.forEach((item, index) => {
            const itemTotal = item.price * item.quantity;
            total += itemTotal;

            html += `
                <div class="flex items-center gap-4 bg-[#fcf8f8] px-4 min-h-[72px] py-2 justify-between border-b border-[#f3e7e8]">
                    <div class="flex flex-col justify-center flex-1">
                        <p class="text-[#1b0e0e] text-base font-medium leading-normal line-clamp-1">${item.name}</p>
                        <p class="text-[#994d51] text-sm font-normal leading-normal line-clamp-2">Quantidade: ${item.quantity}</p>
                    </div>
                    <div class="flex items-center gap-2">
                        <button onclick="updateQuantity(${index}, -1)" class="w-8 h-8 rounded-full bg-[#f3e7e8] flex items-center justify-center text-[#1b0e0e]">-</button>
                        <span class="text-[#1b0e0e] font-medium min-w-[20px] text-center">${item.quantity}</span>
                        <button onclick="updateQuantity(${index}, 1)" class="w-8 h-8 rounded-full bg-[#1b0e0e] flex items-center justify-center text-white">+</button>
                    </div>
                    <div class="shrink-0 ml-4">
                        <p class="text-[#1b0e0e] text-base font-normal leading-normal">R$ ${itemTotal.toFixed(2).replace('.', ',')}</p>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
        totalDisplay.textContent = total.toFixed(2).replace('.', ',');
    }

    function proceedToOrder() {
        if (cart.length === 0) {
            alert('Seu carrinho está vazio!');
            return;
        }

        // Populate order summary
        const summaryContainer = document.getElementById('order-summary-items');
        const orderTotal = document.getElementById('order-total');

        let html = '';
        let total = 0;

        cart.forEach(item => {
            const itemTotal = item.price * item.quantity;
            total += itemTotal;

            html += `
                <div class="flex justify-between items-center mb-1">
                    <span class="text-[#1b0e0e] text-sm">${item.name} (${item.quantity}x)</span>
                    <span class="text-[#1b0e0e] text-sm">R$ ${itemTotal.toFixed(2).replace('.', ',')}</span>
                </div>
            `;
        });

        summaryContainer.innerHTML = html;
        orderTotal.textContent = total.toFixed(2).replace('.', ',');

        document.getElementById('order-modal').classList.remove('hidden');
    }

    function closeOrderModal() {
        document.getElementById('order-modal').classList.add('hidden');
    }

    // Handle form submission
    document.getElementById('order-form').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const orderData = {
            customer_name: formData.get('customer_name'),
            special_requests: formData.get('special_requests'),
            items: cart,
            establishment_id: {{ $establishment->id }},
            location_id: {{ $location->id }}
        };

        // Here you would normally send the order to the server
        // For now, we'll simulate a successful order

        // Clear cart and redirect to confirmation
        cart = [];
        updateCartDisplay();
        renderCartItems();
        closeOrderModal();

        // Redirect to order confirmation page
        window.location.href = '{{ route("customer.pwa.order-confirmation", [$establishment, $location]) }}';
    });

    // Initialize cart display when page loads
    document.addEventListener('DOMContentLoaded', function() {
        renderCartItems();
    });

    // Override updateCartDisplay to also update this page
    const originalUpdateCartDisplay = updateCartDisplay;
    updateCartDisplay = function() {
        originalUpdateCartDisplay();
        if (typeof renderCartItems === 'function') {
            renderCartItems();
        }
    };
</script>
@endsection
